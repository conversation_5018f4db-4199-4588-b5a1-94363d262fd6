<template>
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <!-- 标题 -->
      <div class="text-center mb-16">
        <h2 class="section-title">专家医生团队</h2>
        <p class="section-subtitle">
          汇聚国内外知名医美专家，为您提供专业、安全的医疗服务
        </p>
      </div>
      
      <!-- 医生卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div 
          v-for="doctor in doctors" 
          :key="doctor.id"
          class="card group cursor-pointer"
          @click="viewDoctor(doctor)"
        >
          <div class="relative overflow-hidden">
            <img 
              :src="doctor.avatar" 
              :alt="doctor.name"
              class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-500"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <p class="text-sm">查看详情 →</p>
            </div>
          </div>
          
          <div class="p-6">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-xl font-semibold text-gray-800">{{ doctor.name }}</h3>
              <div class="flex">
                <span 
                  v-for="i in 5" 
                  :key="i"
                  :class="[
                    'w-4 h-4',
                    i <= doctor.rating ? 'text-yellow-400' : 'text-gray-300'
                  ]"
                >
                  ⭐
                </span>
              </div>
            </div>
            
            <p class="text-purple-600 font-medium mb-2">{{ doctor.title }}</p>
            <p class="text-gray-600 text-sm mb-4">{{ doctor.experience }}</p>
            
            <!-- 专长标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span 
                v-for="specialty in doctor.specialties" 
                :key="specialty"
                class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full"
              >
                {{ specialty }}
              </span>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ doctor.cases }}+</div>
                <div class="text-xs text-gray-500">成功案例</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ doctor.years }}年</div>
                <div class="text-xs text-gray-500">从业经验</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 查看全部医生 -->
      <div class="text-center mt-12">
        <NuxtLink to="/doctors" class="btn-primary">
          查看全部医生
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup>
const doctors = [
  {
    id: 1,
    name: '张美丽',
    title: '主任医师 / 整形外科专家',
    experience: '北京协和医学院博士，从事整形外科20余年',
    avatar: '/images/doctor-1.jpg',
    rating: 5,
    specialties: ['面部整形', '鼻部整形', '眼部整形'],
    cases: 5000,
    years: 20
  },
  {
    id: 2,
    name: '李俊华',
    title: '副主任医师 / 皮肤美容专家',
    experience: '上海交通大学医学院硕士，皮肤美容领域专家',
    avatar: '/images/doctor-2.jpg',
    rating: 5,
    specialties: ['激光美容', '注射美容', '皮肤管理'],
    cases: 3500,
    years: 15
  },
  {
    id: 3,
    name: '王雅琳',
    title: '主治医师 / 抗衰老专家',
    experience: '中山大学医学院硕士，专注抗衰老医学研究',
    avatar: '/images/doctor-3.jpg',
    rating: 4,
    specialties: ['抗衰老', '线雕提升', '注射美容'],
    cases: 2800,
    years: 12
  }
]

const viewDoctor = (doctor) => {
  // 跳转到医生详情页
  console.log('查看医生:', doctor.name)
}
</script>
