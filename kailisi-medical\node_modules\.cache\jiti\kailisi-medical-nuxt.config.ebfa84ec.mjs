"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
  app: {
    head: {
      title: '凯丽思医美 - 专业医疗美容机构',
      meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'description', content: '凯丽思医美是专业的医疗美容机构，提供安全、专业的医美服务' }],

      link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }]

    }
  }
}); /* v9-6026adf443b73a02 */
