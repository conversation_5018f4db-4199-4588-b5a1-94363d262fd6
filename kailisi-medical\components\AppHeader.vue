<template>
  <header class="bg-white shadow-lg sticky top-0 z-50">
    <!-- 顶部信息栏 -->
    <div class="bg-purple-600 text-white py-2">
      <div class="container mx-auto px-4 flex justify-between items-center text-sm">
        <div class="flex items-center space-x-6">
          <span>📞 咨询热线：0851-8686</span>
          <span>🏥 AAAAA级医美机构</span>
        </div>
        <div class="flex items-center space-x-4">
          <span>评审：中国整形美容协会</span>
        </div>
      </div>
    </div>
    
    <!-- 主导航 -->
    <nav class="container mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-primary-500 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">凯</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-800">凯丽思</h1>
            <p class="text-sm text-gray-600">专业医疗美容机构</p>
          </div>
        </div>
        
        <!-- 导航菜单 -->
        <div class="hidden md:flex items-center space-x-8">
          <NuxtLink to="/" class="nav-link-dark">网站首页</NuxtLink>
          <NuxtLink to="/about" class="nav-link-dark">医院概况</NuxtLink>
          <NuxtLink to="/doctors" class="nav-link-dark">医生团队</NuxtLink>
          <NuxtLink to="/news" class="nav-link-dark">新闻资讯</NuxtLink>
          <NuxtLink to="/services" class="nav-link-dark">科室导航</NuxtLink>
          <NuxtLink to="/qualifications" class="nav-link-dark">医院资质</NuxtLink>
          <NuxtLink to="/contact" class="nav-link-dark">联系我们</NuxtLink>
        </div>
        
        <!-- 预约按钮 -->
        <div class="hidden md:block">
          <button class="btn-primary">
            在线预约
          </button>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <button @click="toggleMobileMenu" class="md:hidden p-2">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
      
      <!-- 移动端菜单 -->
      <div v-show="showMobileMenu" class="md:hidden mt-4 pb-4 border-t border-gray-200">
        <div class="flex flex-col space-y-3 pt-4">
          <NuxtLink to="/" class="nav-link-dark">网站首页</NuxtLink>
          <NuxtLink to="/about" class="nav-link-dark">医院概况</NuxtLink>
          <NuxtLink to="/doctors" class="nav-link-dark">医生团队</NuxtLink>
          <NuxtLink to="/news" class="nav-link-dark">新闻资讯</NuxtLink>
          <NuxtLink to="/services" class="nav-link-dark">科室导航</NuxtLink>
          <NuxtLink to="/qualifications" class="nav-link-dark">医院资质</NuxtLink>
          <NuxtLink to="/contact" class="nav-link-dark">联系我们</NuxtLink>
          <button class="btn-primary w-full mt-4">在线预约</button>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
const showMobileMenu = ref(false)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}
</script>

<style scoped>
.nav-link-dark {
  @apply text-gray-700 hover:text-purple-600 transition-colors duration-300 font-medium;
}
</style>
