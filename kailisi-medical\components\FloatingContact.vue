<template>
  <div class="fixed right-4 bottom-4 z-50 flex flex-col space-y-3">
    <!-- 在线咨询 -->
    <button 
      @click="openChat"
      class="bg-gradient-to-r from-purple-600 to-primary-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      title="在线咨询"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>
    </button>
    
    <!-- 电话咨询 -->
    <a 
      href="tel:0851-8686"
      class="bg-green-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      title="电话咨询"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
      </svg>
    </a>
    
    <!-- 微信咨询 -->
    <button 
      @click="showWechat = !showWechat"
      class="bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      title="微信咨询"
    >
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.194.468-.78.730-1.673.730-2.615 0-4.054-3.891-7.342-8.691-7.342zm-2.363 5.524a.877.877 0 1 1 0-1.754.877.877 0 0 1 0 1.754zm4.725 0a.877.877 0 1 1 0-1.754.877.877 0 0 1 0 1.754z"/>
        <path d="M15.309 9.776c-3.516 0-6.374 2.33-6.374 5.204 0 1.665.886 3.175 2.275 4.191a.446.446 0 0 1 .161.503l-.295 1.119c-.014.053-.037.106-.037.161 0 .122.098.223.219.223a.25.25 0 0 0 .126-.041l1.441-.842a.652.652 0 0 1 .542-.074c.586.187 1.213.292 1.942.292 3.516 0 6.374-2.33 6.374-5.204s-2.858-5.204-6.374-5.204zm-1.896 4.191a.663.663 0 1 1 0-1.326.663.663 0 0 1 0 1.326zm3.793 0a.663.663 0 1 1 0-1.326.663.663 0 0 1 0 1.326z"/>
      </svg>
    </button>
    
    <!-- 微信二维码弹窗 -->
    <div v-show="showWechat" class="absolute right-0 bottom-20 bg-white p-4 rounded-lg shadow-xl border">
      <div class="text-center">
        <div class="w-32 h-32 bg-gray-200 rounded-lg mb-2 flex items-center justify-center">
          <span class="text-gray-500 text-sm">微信二维码</span>
        </div>
        <p class="text-sm text-gray-600">扫码添加微信咨询</p>
      </div>
    </div>
    
    <!-- 返回顶部 -->
    <button 
      v-show="showBackToTop"
      @click="scrollToTop"
      class="bg-gray-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      title="返回顶部"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
      </svg>
    </button>
  </div>
</template>

<script setup>
const showWechat = ref(false)
const showBackToTop = ref(false)

const openChat = () => {
  // 这里可以集成在线客服系统
  alert('在线咨询功能开发中...')
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听滚动事件
onMounted(() => {
  const handleScroll = () => {
    showBackToTop.value = window.scrollY > 300
  }
  
  window.addEventListener('scroll', handleScroll)
  
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
  })
})
</script>
