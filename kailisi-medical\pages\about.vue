<template>
  <div>
    <!-- 页面头部 -->
    <section class="hero-gradient py-20">
      <div class="container mx-auto px-4 text-center text-white">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">医院概况</h1>
        <p class="text-xl opacity-90">专业医疗美容机构，致力于为您提供安全、专业的医美服务</p>
      </div>
    </section>
    
    <!-- 医院介绍 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl font-bold text-gray-800 mb-6">关于凯丽思医美</h2>
            <p class="text-gray-600 mb-6 leading-relaxed">
              凯丽思医美成立于2014年，是一家专业的医疗美容机构。我们秉承"安全第一、效果至上"的理念，
              为广大爱美人士提供专业、安全、个性化的医美服务。
            </p>
            <p class="text-gray-600 mb-6 leading-relaxed">
              经过10年的发展，凯丽思医美已成为AAAAA级医美机构，拥有三级整形外科医院资质，
              获得中国整形美容协会认证，是贵阳地区领先的医美品牌。
            </p>
            <div class="grid grid-cols-2 gap-6">
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">10+</div>
                <div class="text-sm text-gray-600">年专业经验</div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">50+</div>
                <div class="text-sm text-gray-600">专业医师</div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">10万+</div>
                <div class="text-sm text-gray-600">成功案例</div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">98%</div>
                <div class="text-sm text-gray-600">客户满意度</div>
              </div>
            </div>
          </div>
          <div>
            <img src="/images/about-hospital.jpg" alt="凯丽思医美" class="w-full rounded-xl shadow-lg" />
          </div>
        </div>
      </div>
    </section>
    
    <!-- 发展历程 -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="section-title">发展历程</h2>
        <div class="max-w-4xl mx-auto">
          <div class="space-y-8">
            <div v-for="milestone in milestones" :key="milestone.year" class="flex items-start space-x-6">
              <div class="flex-shrink-0 w-20 h-20 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
                {{ milestone.year }}
              </div>
              <div class="flex-1 bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ milestone.title }}</h3>
                <p class="text-gray-600">{{ milestone.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 医院环境 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="section-title">医院环境</h2>
        <p class="section-subtitle">舒适优雅的就医环境，让您享受VIP级别的医美体验</p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="env in environments" :key="env.id" class="card group">
            <div class="relative overflow-hidden h-64">
              <img :src="env.image" :alt="env.name" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
            </div>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ env.name }}</h3>
              <p class="text-gray-600">{{ env.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useHead({
  title: '医院概况 - 凯丽思医美',
  meta: [
    { name: 'description', content: '了解凯丽思医美的发展历程、医院环境和专业实力，AAAAA级医美机构，10年专业经验。' }
  ]
})

const milestones = [
  {
    year: '2014',
    title: '凯丽思医美成立',
    description: '在贵阳市云岩区正式成立，开始为广大爱美人士提供专业医美服务。'
  },
  {
    year: '2016',
    title: '获得三级整形外科医院资质',
    description: '通过严格的评审，获得三级整形外科医院资质，医疗水平得到权威认可。'
  },
  {
    year: '2018',
    title: '引进国际先进设备',
    description: '大规模引进国际先进医美设备，技术水平达到国际领先标准。'
  },
  {
    year: '2020',
    title: '获得AAAAA级医美机构认证',
    description: '通过中国整形美容协会严格评审，获得AAAAA级医美机构认证。'
  },
  {
    year: '2022',
    title: '成功案例突破10万例',
    description: '累计成功案例突破10万例，客户满意度持续保持在98%以上。'
  },
  {
    year: '2024',
    title: '10周年庆典',
    description: '迎来10周年庆典，推出会员奢宠季活动，感谢广大客户的信任与支持。'
  }
]

const environments = [
  {
    id: 1,
    name: '接待大厅',
    description: '宽敞明亮的接待大厅，温馨舒适的环境让您倍感放松。',
    image: '/images/env-lobby.jpg'
  },
  {
    id: 2,
    name: '咨询室',
    description: '私密的一对一咨询空间，专业顾问为您详细解答。',
    image: '/images/env-consultation.jpg'
  },
  {
    id: 3,
    name: '手术室',
    description: '无菌标准手术室，先进设备确保手术安全。',
    image: '/images/env-surgery.jpg'
  },
  {
    id: 4,
    name: '恢复室',
    description: '舒适的术后恢复环境，专业护理团队贴心照护。',
    image: '/images/env-recovery.jpg'
  },
  {
    id: 5,
    name: '美容治疗室',
    description: '专业的美容治疗空间，配备最新美容设备。',
    image: '/images/env-treatment.jpg'
  },
  {
    id: 6,
    name: 'VIP休息区',
    description: '高端VIP休息区，为您提供尊贵的服务体验。',
    image: '/images/env-vip.jpg'
  }
]
</script>
