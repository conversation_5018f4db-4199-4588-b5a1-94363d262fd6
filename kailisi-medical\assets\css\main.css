@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-primary-500 text-white px-6 py-3 rounded-lg font-medium hover:from-purple-700 hover:to-primary-600 transition-all duration-300 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-white text-purple-600 border-2 border-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-purple-50 transition-all duration-300;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold text-gray-800 text-center mb-4;
  }
  
  .section-subtitle {
    @apply text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto;
  }
  
  .nav-link {
    @apply text-white hover:text-purple-200 transition-colors duration-300 font-medium;
  }
  
  .hero-gradient {
    @apply bg-gradient-to-br from-purple-600 via-primary-500 to-purple-700;
  }
}
