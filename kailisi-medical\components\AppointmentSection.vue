<template>
  <section class="py-20 hero-gradient">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center text-white">
        <!-- 标题 -->
        <h2 class="text-4xl md:text-5xl font-bold mb-6">
          开启您的美丽之旅
        </h2>
        <p class="text-xl mb-12 opacity-90">
          专业医美团队为您提供个性化美丽方案，让美丽触手可及
        </p>
        
        <!-- 预约表单 -->
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-12">
          <h3 class="text-2xl font-semibold mb-6">免费咨询预约</h3>
          <form @submit.prevent="submitAppointment" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <input
                v-model="form.name"
                type="text"
                placeholder="您的姓名"
                class="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                required
              />
            </div>
            <div>
              <input
                v-model="form.phone"
                type="tel"
                placeholder="联系电话"
                class="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                required
              />
            </div>
            <div>
              <select
                v-model="form.service"
                class="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                required
              >
                <option value="" disabled class="text-gray-800">选择咨询项目</option>
                <option value="face" class="text-gray-800">面部整形</option>
                <option value="skin" class="text-gray-800">皮肤美容</option>
                <option value="body" class="text-gray-800">身体塑形</option>
                <option value="antiaging" class="text-gray-800">抗衰老</option>
                <option value="other" class="text-gray-800">其他项目</option>
              </select>
            </div>
            <div>
              <input
                v-model="form.date"
                type="date"
                class="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                :min="minDate"
                required
              />
            </div>
            <div class="md:col-span-2">
              <textarea
                v-model="form.message"
                placeholder="详细描述您的需求（可选）"
                rows="3"
                class="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none"
              ></textarea>
            </div>
            <div class="md:col-span-2">
              <button
                type="submit"
                :disabled="isSubmitting"
                class="w-full bg-white text-purple-600 font-semibold py-4 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isSubmitting ? '提交中...' : '立即预约' }}
              </button>
            </div>
          </form>
        </div>
        
        <!-- 联系方式 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold mb-2">电话咨询</h4>
            <p class="opacity-90">0851-8686</p>
            <p class="text-sm opacity-75">24小时咨询热线</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.194.468-.78.730-1.673.730-2.615 0-4.054-3.891-7.342-8.691-7.342zm-2.363 5.524a.877.877 0 1 1 0-1.754.877.877 0 0 1 0 1.754zm4.725 0a.877.877 0 1 1 0-1.754.877.877 0 0 1 0 1.754z"/>
              </svg>
            </div>
            <h4 class="text-lg font-semibold mb-2">微信咨询</h4>
            <p class="opacity-90">扫码添加</p>
            <p class="text-sm opacity-75">专业顾问在线</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold mb-2">到院咨询</h4>
            <p class="opacity-90">贵阳市云岩区</p>
            <p class="text-sm opacity-75">9:00-18:00</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const form = reactive({
  name: '',
  phone: '',
  service: '',
  date: '',
  message: ''
})

const isSubmitting = ref(false)

// 获取最小日期（今天）
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const submitAppointment = async () => {
  isSubmitting.value = true
  
  try {
    // 这里应该调用API提交预约信息
    console.log('提交预约:', form)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('预约提交成功！我们会尽快联系您。')
    
    // 重置表单
    Object.keys(form).forEach(key => {
      form[key] = ''
    })
  } catch (error) {
    console.error('预约提交失败:', error)
    alert('预约提交失败，请稍后重试或直接电话联系我们。')
  } finally {
    isSubmitting.value = false
  }
}
</script>
