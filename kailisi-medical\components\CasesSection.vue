<template>
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <!-- 标题 -->
      <div class="text-center mb-16">
        <h2 class="section-title">真实案例展示</h2>
        <p class="section-subtitle">
          见证每一次美丽蜕变，分享真实的变美故事
        </p>
      </div>
      
      <!-- 案例分类 -->
      <div class="flex flex-wrap justify-center gap-4 mb-12">
        <button
          v-for="category in categories"
          :key="category.id"
          @click="activeCategory = category.id"
          :class="[
            'px-6 py-3 rounded-full font-medium transition-all duration-300',
            activeCategory === category.id
              ? 'bg-purple-600 text-white shadow-lg'
              : 'bg-white text-gray-600 hover:bg-purple-50 hover:text-purple-600'
          ]"
        >
          {{ category.name }}
        </button>
      </div>
      
      <!-- 案例网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div 
          v-for="case_ in filteredCases" 
          :key="case_.id"
          class="card group cursor-pointer"
          @click="viewCase(case_)"
        >
          <!-- 对比图片 -->
          <div class="relative overflow-hidden h-64">
            <div class="flex h-full">
              <div class="w-1/2 relative">
                <img 
                  :src="case_.beforeImage" 
                  :alt="`${case_.title} - 术前`"
                  class="w-full h-full object-cover"
                />
                <div class="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  术前
                </div>
              </div>
              <div class="w-1/2 relative">
                <img 
                  :src="case_.afterImage" 
                  :alt="`${case_.title} - 术后`"
                  class="w-full h-full object-cover"
                />
                <div class="absolute top-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded">
                  术后
                </div>
              </div>
            </div>
            <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="bg-white/90 text-gray-800 px-4 py-2 rounded-lg font-medium">
                查看详情
              </div>
            </div>
          </div>
          
          <!-- 案例信息 -->
          <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ case_.title }}</h3>
            <p class="text-gray-600 text-sm mb-3">{{ case_.description }}</p>
            
            <!-- 项目标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span 
                v-for="project in case_.projects" 
                :key="project"
                class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full"
              >
                {{ project }}
              </span>
            </div>
            
            <!-- 医生和时间 -->
            <div class="flex justify-between items-center text-sm text-gray-500">
              <span>主刀医生：{{ case_.doctor }}</span>
              <span>{{ case_.date }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 查看更多 -->
      <div class="text-center mt-12">
        <button class="btn-primary">
          查看更多案例
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>
const activeCategory = ref('all')

const categories = [
  { id: 'all', name: '全部案例' },
  { id: 'face', name: '面部整形' },
  { id: 'skin', name: '皮肤美容' },
  { id: 'body', name: '身体塑形' },
  { id: 'antiaging', name: '抗衰老' }
]

const cases = [
  {
    id: 1,
    title: '双眼皮+开眼角案例',
    description: '通过精细的双眼皮手术和开眼角，让眼睛更加有神',
    category: 'face',
    beforeImage: '/images/case-1-before.jpg',
    afterImage: '/images/case-1-after.jpg',
    projects: ['双眼皮', '开眼角'],
    doctor: '张美丽',
    date: '2024-03-15'
  },
  {
    id: 2,
    title: '激光祛斑美白案例',
    description: '采用先进激光技术，有效去除面部色斑，肌肤更加白皙',
    category: 'skin',
    beforeImage: '/images/case-2-before.jpg',
    afterImage: '/images/case-2-after.jpg',
    projects: ['激光祛斑', '美白嫩肤'],
    doctor: '李俊华',
    date: '2024-03-10'
  },
  {
    id: 3,
    title: '鼻综合整形案例',
    description: '通过鼻综合手术，打造立体精致的鼻型',
    category: 'face',
    beforeImage: '/images/case-3-before.jpg',
    afterImage: '/images/case-3-after.jpg',
    projects: ['隆鼻', '鼻翼缩小'],
    doctor: '张美丽',
    date: '2024-03-05'
  },
  {
    id: 4,
    title: '面部线雕提升案例',
    description: '无创线雕技术，有效提升面部轮廓，重现年轻状态',
    category: 'antiaging',
    beforeImage: '/images/case-4-before.jpg',
    afterImage: '/images/case-4-after.jpg',
    projects: ['线雕提升', '面部紧致'],
    doctor: '王雅琳',
    date: '2024-02-28'
  },
  {
    id: 5,
    title: '吸脂塑形案例',
    description: '精准吸脂技术，塑造完美身材曲线',
    category: 'body',
    beforeImage: '/images/case-5-before.jpg',
    afterImage: '/images/case-5-after.jpg',
    projects: ['腰腹吸脂', '大腿吸脂'],
    doctor: '张美丽',
    date: '2024-02-20'
  },
  {
    id: 6,
    title: '水光针嫩肤案例',
    description: '水光针注射，深层补水，肌肤水润有光泽',
    category: 'skin',
    beforeImage: '/images/case-6-before.jpg',
    afterImage: '/images/case-6-after.jpg',
    projects: ['水光针', '补水嫩肤'],
    doctor: '李俊华',
    date: '2024-02-15'
  }
]

const filteredCases = computed(() => {
  if (activeCategory.value === 'all') {
    return cases
  }
  return cases.filter(case_ => case_.category === activeCategory.value)
})

const viewCase = (case_) => {
  // 跳转到案例详情页
  console.log('查看案例:', case_.title)
}
</script>
