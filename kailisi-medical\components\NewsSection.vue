<template>
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <!-- 标题 -->
      <div class="text-center mb-16">
        <h2 class="section-title">新闻资讯</h2>
        <p class="section-subtitle">
          了解最新医美资讯，掌握美丽趋势
        </p>
      </div>
      
      <!-- 新闻网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 主要新闻 -->
        <div class="lg:col-span-2">
          <div class="card group cursor-pointer h-full" @click="viewNews(featuredNews)">
            <div class="relative overflow-hidden h-64 lg:h-80">
              <img 
                :src="featuredNews.image" 
                :alt="featuredNews.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div class="absolute bottom-6 left-6 text-white">
                <div class="flex items-center space-x-2 mb-2">
                  <span class="bg-purple-600 text-xs px-2 py-1 rounded">{{ featuredNews.category }}</span>
                  <span class="text-sm opacity-90">{{ featuredNews.date }}</span>
                </div>
                <h3 class="text-xl lg:text-2xl font-bold mb-2">{{ featuredNews.title }}</h3>
                <p class="text-sm opacity-90 line-clamp-2">{{ featuredNews.summary }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 侧边新闻列表 -->
        <div class="space-y-6">
          <div 
            v-for="news in sideNews" 
            :key="news.id"
            class="card group cursor-pointer"
            @click="viewNews(news)"
          >
            <div class="flex">
              <div class="w-24 h-24 flex-shrink-0 overflow-hidden rounded-l-xl">
                <img 
                  :src="news.image" 
                  :alt="news.title"
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div class="flex-1 p-4">
                <div class="flex items-center space-x-2 mb-2">
                  <span class="bg-purple-100 text-purple-600 text-xs px-2 py-1 rounded">{{ news.category }}</span>
                  <span class="text-xs text-gray-500">{{ news.date }}</span>
                </div>
                <h4 class="font-semibold text-gray-800 mb-1 line-clamp-2 group-hover:text-purple-600 transition-colors">
                  {{ news.title }}
                </h4>
                <p class="text-sm text-gray-600 line-clamp-2">{{ news.summary }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 更多新闻 -->
      <div class="mt-16">
        <h3 class="text-xl font-semibold text-gray-800 mb-8">更多资讯</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div 
            v-for="news in moreNews" 
            :key="news.id"
            class="card group cursor-pointer"
            @click="viewNews(news)"
          >
            <div class="relative overflow-hidden h-40">
              <img 
                :src="news.image" 
                :alt="news.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-2 left-2">
                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">{{ news.category }}</span>
              </div>
            </div>
            <div class="p-4">
              <h4 class="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-purple-600 transition-colors">
                {{ news.title }}
              </h4>
              <p class="text-sm text-gray-600 mb-2 line-clamp-2">{{ news.summary }}</p>
              <div class="text-xs text-gray-500">{{ news.date }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 查看更多 -->
      <div class="text-center mt-12">
        <NuxtLink to="/news" class="btn-primary">
          查看更多资讯
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup>
const featuredNews = {
  id: 1,
  title: '凯丽思医美10周年庆典盛大开幕',
  summary: '凯丽思医美迎来10周年庆典，推出多项优惠活动，感谢广大客户的信任与支持。',
  image: '/images/news-featured.jpg',
  category: '医院动态',
  date: '2024-03-20'
}

const sideNews = [
  {
    id: 2,
    title: '春季护肤指南：如何选择适合的医美项目',
    summary: '春季是护肤的黄金时期，专家为您推荐最适合的医美护肤方案。',
    image: '/images/news-2.jpg',
    category: '护肤知识',
    date: '2024-03-18'
  },
  {
    id: 3,
    title: '新技术引进：第五代热玛吉正式启用',
    summary: '凯丽思医美引进最新第五代热玛吉设备，为客户提供更好的抗衰老体验。',
    image: '/images/news-3.jpg',
    category: '技术更新',
    date: '2024-03-15'
  },
  {
    id: 4,
    title: '专家访谈：面部年轻化的最新趋势',
    summary: '知名整形专家分享面部年轻化的最新技术和发展趋势。',
    image: '/images/news-4.jpg',
    category: '专家观点',
    date: '2024-03-12'
  }
]

const moreNews = [
  {
    id: 5,
    title: '夏季防晒与医美护肤的完美结合',
    summary: '夏季护肤要点和医美项目的最佳搭配方案。',
    image: '/images/news-5.jpg',
    category: '护肤知识',
    date: '2024-03-10'
  },
  {
    id: 6,
    title: '客户分享：我的变美之路',
    summary: '真实客户分享在凯丽思的变美经历和心得体会。',
    image: '/images/news-6.jpg',
    category: '客户故事',
    date: '2024-03-08'
  },
  {
    id: 7,
    title: '医美安全知识：如何选择正规机构',
    summary: '专业指导如何选择安全可靠的医美机构。',
    image: '/images/news-7.jpg',
    category: '安全知识',
    date: '2024-03-05'
  },
  {
    id: 8,
    title: '新品发布：引进韩国最新美容技术',
    summary: '凯丽思医美引进韩国最新美容技术和设备。',
    image: '/images/news-8.jpg',
    category: '技术更新',
    date: '2024-03-01'
  }
]

const viewNews = (news) => {
  // 跳转到新闻详情页
  console.log('查看新闻:', news.title)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
