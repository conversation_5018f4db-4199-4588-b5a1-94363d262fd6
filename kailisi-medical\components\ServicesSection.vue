<template>
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <!-- 标题 -->
      <div class="text-center mb-16">
        <h2 class="section-title">专业服务项目</h2>
        <p class="section-subtitle">
          凯丽思医美提供全方位的医疗美容服务，让您的美丽更加自信
        </p>
      </div>
      
      <!-- 服务网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div 
          v-for="service in services" 
          :key="service.id"
          class="card group cursor-pointer"
          @click="selectService(service)"
        >
          <div class="relative overflow-hidden">
            <img 
              :src="service.image" 
              :alt="service.name"
              class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <p class="text-sm">了解更多 →</p>
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-primary-500 rounded-lg flex items-center justify-center mr-4">
                <component :is="service.icon" class="w-6 h-6 text-white" />
              </div>
              <h3 class="text-xl font-semibold text-gray-800">{{ service.name }}</h3>
            </div>
            <p class="text-gray-600 mb-4">{{ service.description }}</p>
            <div class="flex flex-wrap gap-2">
              <span 
                v-for="tag in service.tags" 
                :key="tag"
                class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 更多服务按钮 -->
      <div class="text-center mt-12">
        <NuxtLink to="/services" class="btn-primary">
          查看全部服务
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup>
// 图标组件（简化版）
const FaceIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>`
}

const SkinIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>`
}

const BodyIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>`
}

const AntiAgingIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>`
}

const services = [
  {
    id: 1,
    name: '面部整形',
    description: '专业的面部轮廓塑造，让您拥有精致五官',
    image: '/images/service-face.jpg',
    icon: FaceIcon,
    tags: ['双眼皮', '隆鼻', '瘦脸', '下颌角']
  },
  {
    id: 2,
    name: '皮肤美容',
    description: '先进的皮肤管理技术，重现年轻肌肤',
    image: '/images/service-skin.jpg',
    icon: SkinIcon,
    tags: ['光子嫩肤', '激光祛斑', '水光针', '热玛吉']
  },
  {
    id: 3,
    name: '身体塑形',
    description: '科学的身材管理方案，塑造完美曲线',
    image: '/images/service-body.jpg',
    icon: BodyIcon,
    tags: ['吸脂塑形', '隆胸', '提臀', '腰腹塑形']
  },
  {
    id: 4,
    name: '抗衰老',
    description: '综合抗衰方案，延缓岁月痕迹',
    image: '/images/service-antiaging.jpg',
    icon: AntiAgingIcon,
    tags: ['肉毒素', '玻尿酸', '线雕', 'PRP']
  }
]

const selectService = (service) => {
  // 这里可以跳转到服务详情页或打开模态框
  console.log('选择服务:', service.name)
}
</script>
