<template>
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <!-- 标题 -->
      <div class="text-center mb-16">
        <h2 class="section-title">选择凯丽思的理由</h2>
        <p class="section-subtitle">
          专业、安全、贴心的医美服务，让您的美丽之路更加安心
        </p>
      </div>
      
      <!-- 优势网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div 
          v-for="advantage in advantages" 
          :key="advantage.id"
          class="text-center group"
        >
          <!-- 图标 -->
          <div class="w-20 h-20 bg-gradient-to-br from-purple-600 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <component :is="advantage.icon" class="w-10 h-10 text-white" />
          </div>
          
          <!-- 内容 -->
          <h3 class="text-xl font-semibold text-gray-800 mb-3">{{ advantage.title }}</h3>
          <p class="text-gray-600 leading-relaxed">{{ advantage.description }}</p>
          
          <!-- 数据展示 -->
          <div v-if="advantage.stats" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ advantage.stats.value }}</div>
            <div class="text-sm text-gray-500">{{ advantage.stats.label }}</div>
          </div>
        </div>
      </div>
      
      <!-- 认证展示 -->
      <div class="mt-20 bg-gray-50 rounded-2xl p-8">
        <h3 class="text-2xl font-bold text-center text-gray-800 mb-8">权威认证</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div 
            v-for="cert in certifications" 
            :key="cert.id"
            class="text-center"
          >
            <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
              <img :src="cert.logo" :alt="cert.name" class="w-12 h-12 object-contain" />
            </div>
            <p class="text-sm font-medium text-gray-700">{{ cert.name }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// 图标组件
const SafetyIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path></svg>`
}

const ExpertIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>`
}

const TechIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364-.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>`
}

const ServiceIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>`
}

const advantages = [
  {
    id: 1,
    title: '安全保障',
    description: 'AAAAA级医美机构认证，严格的安全管理体系，确保每一次治疗的安全性',
    icon: SafetyIcon,
    stats: {
      value: '0',
      label: '医疗事故'
    }
  },
  {
    id: 2,
    title: '专家团队',
    description: '汇聚国内外知名医美专家，平均从业经验15年以上，技术精湛',
    icon: ExpertIcon,
    stats: {
      value: '50+',
      label: '专业医师'
    }
  },
  {
    id: 3,
    title: '先进设备',
    description: '引进国际先进医美设备，采用最新技术，确保治疗效果',
    icon: TechIcon,
    stats: {
      value: '100+',
      label: '先进设备'
    }
  },
  {
    id: 4,
    title: '贴心服务',
    description: '一对一专属顾问服务，全程跟踪，让您享受VIP级别的医美体验',
    icon: ServiceIcon,
    stats: {
      value: '98%',
      label: '满意度'
    }
  }
]

const certifications = [
  {
    id: 1,
    name: 'AAAAA级医美机构',
    logo: '/images/cert-1.png'
  },
  {
    id: 2,
    name: '三级整形外科医院',
    logo: '/images/cert-2.png'
  },
  {
    id: 3,
    name: '中国整形美容协会',
    logo: '/images/cert-3.png'
  },
  {
    id: 4,
    name: 'ISO质量认证',
    logo: '/images/cert-4.png'
  }
]
</script>
