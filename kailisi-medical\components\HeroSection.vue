<template>
  <section class="relative h-screen flex items-center justify-center overflow-hidden">
    <!-- 背景轮播 -->
    <div class="absolute inset-0">
      <div 
        v-for="(slide, index) in slides" 
        :key="index"
        :class="[
          'absolute inset-0 transition-opacity duration-1000',
          currentSlide === index ? 'opacity-100' : 'opacity-0'
        ]"
      >
        <div class="absolute inset-0 hero-gradient opacity-90"></div>
        <img 
          :src="slide.image" 
          :alt="slide.title"
          class="w-full h-full object-cover"
        />
      </div>
    </div>
    
    <!-- 内容 -->
    <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
      <div class="mb-8">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          <span class="block">会员臻享</span>
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">
            为美闪耀
          </span>
        </h1>
        <p class="text-xl md:text-2xl mb-8 opacity-90">
          上市10周年 会员奢宠季
        </p>
        <p class="text-lg mb-12 opacity-80 max-w-2xl mx-auto">
          凯丽思医美，专业医疗美容机构，为您提供安全、专业、个性化的医美服务
        </p>
      </div>
      
      <!-- 行动按钮 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <button class="btn-primary text-lg px-8 py-4">
          立即预约
        </button>
        <button class="btn-secondary text-lg px-8 py-4 bg-white/20 border-white/50 text-white hover:bg-white/30">
          了解更多
        </button>
      </div>
      
      <!-- 特色标签 -->
      <div class="mt-16 flex flex-wrap justify-center gap-6">
        <div class="bg-white/20 backdrop-blur-sm rounded-full px-6 py-3">
          <span class="text-sm font-medium">AAAAA级医美机构</span>
        </div>
        <div class="bg-white/20 backdrop-blur-sm rounded-full px-6 py-3">
          <span class="text-sm font-medium">三级整形外科医院</span>
        </div>
        <div class="bg-white/20 backdrop-blur-sm rounded-full px-6 py-3">
          <span class="text-sm font-medium">上市医美</span>
        </div>
      </div>
    </div>
    
    <!-- 轮播指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
      <button
        v-for="(slide, index) in slides"
        :key="index"
        @click="currentSlide = index"
        :class="[
          'w-3 h-3 rounded-full transition-all duration-300',
          currentSlide === index ? 'bg-white' : 'bg-white/50'
        ]"
      ></button>
    </div>
    
    <!-- 滚动提示 -->
    <div class="absolute bottom-8 right-8 text-white animate-bounce">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
      </svg>
    </div>
  </section>
</template>

<script setup>
const currentSlide = ref(0)

const slides = [
  {
    title: '会员臻享 为美闪耀',
    image: '/images/hero-1.jpg',
    description: '上市10周年 会员奢宠季'
  },
  {
    title: '专业医美 安全保障',
    image: '/images/hero-2.jpg',
    description: 'AAAAA级医美机构'
  },
  {
    title: '个性定制 精准美学',
    image: '/images/hero-3.jpg',
    description: '三级整形外科医院'
  }
]

// 自动轮播
onMounted(() => {
  const interval = setInterval(() => {
    currentSlide.value = (currentSlide.value + 1) % slides.length
  }, 5000)
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>
